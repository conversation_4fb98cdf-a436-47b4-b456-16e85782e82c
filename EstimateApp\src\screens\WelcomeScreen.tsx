import React from 'react';
import { View, Text, StyleSheet, Image, SafeAreaView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { ThemedButton } from '../components/ThemedButton';

interface WelcomeScreenProps {
  onLogin: () => void;
  onRegister: () => void;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onLogin, onRegister }) => {
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 24,
    },
    logoContainer: {
      alignItems: 'center',
      marginBottom: 48,
    },
    logo: {
      width: 120,
      height: 120,
      backgroundColor: theme.colors.primary,
      borderRadius: 60,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 24,
    },
    title: {
      fontSize: 28,
      fontWeight: 'bold',
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: 24,
    },
    featuresContainer: {
      width: '100%',
      marginBottom: 48,
    },
    featureItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
      paddingHorizontal: 16,
    },
    featureIcon: {
      marginRight: 16,
    },
    featureText: {
      fontSize: 16,
      color: theme.colors.text,
      flex: 1,
    },
    buttonContainer: {
      width: '100%',
      gap: 16,
    },
    footer: {
      position: 'absolute',
      bottom: 40,
      alignItems: 'center',
    },
    footerText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
  });

  const features = [
    {
      icon: 'people-outline' as keyof typeof Ionicons.glyphMap,
      text: 'Manage clients and projects efficiently',
    },
    {
      icon: 'calculator-outline' as keyof typeof Ionicons.glyphMap,
      text: 'Create detailed construction estimates',
    },
    {
      icon: 'document-text-outline' as keyof typeof Ionicons.glyphMap,
      text: 'Use pre-built templates for faster quotes',
    },
    {
      icon: 'analytics-outline' as keyof typeof Ionicons.glyphMap,
      text: 'Track project progress and analytics',
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.logoContainer}>
        <View style={styles.logo}>
          <Ionicons name="construct" size={60} color="#FFFFFF" />
        </View>
        <Text style={styles.title}>Construction Estimate Manager</Text>
        <Text style={styles.subtitle}>
          Streamline your construction business with professional estimates and client management
        </Text>
      </View>

      <View style={styles.featuresContainer}>
        {features.map((feature, index) => (
          <View key={index} style={styles.featureItem}>
            <Ionicons
              name={feature.icon}
              size={24}
              color={theme.colors.primary}
              style={styles.featureIcon}
            />
            <Text style={styles.featureText}>{feature.text}</Text>
          </View>
        ))}
      </View>

      <View style={styles.buttonContainer}>
        <ThemedButton
          title="Sign In"
          onPress={onLogin}
          variant="primary"
          size="large"
        />
        <ThemedButton
          title="Create Account"
          onPress={onRegister}
          variant="outline"
          size="large"
        />
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Professional construction estimate management{'\n'}
          Built for contractors, by contractors
        </Text>
      </View>
    </SafeAreaView>
  );
};
