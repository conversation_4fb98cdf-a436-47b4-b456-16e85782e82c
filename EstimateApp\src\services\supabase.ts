import { createClient } from '@supabase/supabase-js';
import 'react-native-url-polyfill/auto';

// Replace these with your actual Supabase project URL and anon key
const supabaseUrl = 'https://your-project-ref.supabase.co';
const supabaseAnonKey = 'your-anon-key';

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Mock data for development - replace with actual Supabase calls
export const mockData = {
  users: [
    {
      id: '1',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Admin',
      role: 'Admin' as const,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
    {
      id: '2',
      email: '<EMAIL>',
      firstName: '<PERSON>',
      lastName: 'Manager',
      role: 'Manager' as const,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
  ],
  clients: [
    {
      id: '1',
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      email: '<EMAIL>',
      phone: '+****************',
      company: 'Johnson Properties',
      status: 'Active' as const,
      address: {
        street: '123 Main St',
        city: 'Springfield',
        state: 'IL',
        zipCode: '62701',
        country: 'USA',
      },
      createdAt: '2024-01-15T00:00:00Z',
      updatedAt: '2024-01-15T00:00:00Z',
    },
    {
      id: '2',
      firstName: 'Sarah',
      lastName: 'Williams',
      email: '<EMAIL>',
      phone: '+****************',
      company: 'Williams Construction',
      status: 'Active' as const,
      address: {
        street: '456 Oak Ave',
        city: 'Springfield',
        state: 'IL',
        zipCode: '62702',
        country: 'USA',
      },
      createdAt: '2024-01-20T00:00:00Z',
      updatedAt: '2024-01-20T00:00:00Z',
    },
  ],
  templates: [
    {
      id: '1',
      name: 'Basic Electrical Work',
      description: 'Standard electrical installation template',
      category: 'Electrical' as const,
      isPublic: true,
      createdBy: '1',
      items: [
        {
          id: '1',
          description: 'Electrical outlet installation',
          unit: 'each',
          estimatedPrice: 75,
          category: 'Installation',
        },
        {
          id: '2',
          description: 'Light fixture installation',
          unit: 'each',
          estimatedPrice: 125,
          category: 'Installation',
        },
      ],
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
    {
      id: '2',
      name: 'Bathroom Renovation',
      description: 'Complete bathroom renovation package',
      category: 'Plumbing' as const,
      isPublic: true,
      createdBy: '1',
      items: [
        {
          id: '3',
          description: 'Toilet installation',
          unit: 'each',
          estimatedPrice: 350,
          category: 'Plumbing',
        },
        {
          id: '4',
          description: 'Sink and faucet installation',
          unit: 'each',
          estimatedPrice: 450,
          category: 'Plumbing',
        },
      ],
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
  ],
  estimates: [
    {
      id: '1',
      clientId: '1',
      title: 'Kitchen Electrical Upgrade',
      description: 'Upgrade kitchen electrical system with new outlets and lighting',
      status: 'Pending' as const,
      items: [
        {
          id: '1',
          description: 'GFCI outlet installation',
          quantity: 4,
          unit: 'each',
          unitPrice: 85,
          total: 340,
          category: 'Electrical',
        },
        {
          id: '2',
          description: 'Under-cabinet LED lighting',
          quantity: 1,
          unit: 'set',
          unitPrice: 450,
          total: 450,
          category: 'Lighting',
        },
      ],
      subtotal: 790,
      tax: 63.20,
      total: 853.20,
      validUntil: '2024-03-01T00:00:00Z',
      createdBy: '1',
      createdAt: '2024-02-01T00:00:00Z',
      updatedAt: '2024-02-01T00:00:00Z',
    },
  ],
};
