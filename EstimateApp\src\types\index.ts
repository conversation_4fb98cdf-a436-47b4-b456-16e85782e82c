// User and Authentication Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  companyId?: string;
  createdAt: string;
  updatedAt: string;
}

export type UserRole = 'Admin' | 'Manager' | 'Employee' | 'Client';

export interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

// Client Types
export interface Client {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: Address;
  company?: string;
  status: ClientStatus;
  createdAt: string;
  updatedAt: string;
}

export type ClientStatus = 'Active' | 'Inactive' | 'Pending';

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

// Estimate Types
export interface Estimate {
  id: string;
  clientId: string;
  client?: Client;
  title: string;
  description: string;
  status: EstimateStatus;
  items: EstimateItem[];
  subtotal: number;
  tax: number;
  total: number;
  validUntil: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export type EstimateStatus = 'Draft' | 'Pending' | 'Approved' | 'Rejected' | 'Expired';

export interface EstimateItem {
  id: string;
  description: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  total: number;
  category: string;
}

// Template Types
export interface Template {
  id: string;
  name: string;
  description: string;
  category: TemplateCategory;
  items: TemplateItem[];
  createdBy: string;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
}

export type TemplateCategory = 
  | 'Electrical' 
  | 'Plumbing' 
  | 'HVAC' 
  | 'Roofing' 
  | 'Flooring' 
  | 'Painting' 
  | 'General Construction' 
  | 'Landscaping';

export interface TemplateItem {
  id: string;
  description: string;
  unit: string;
  estimatedPrice: number;
  category: string;
}

// Dashboard Types
export interface DashboardStats {
  totalEstimates: number;
  pendingApprovals: number;
  totalRevenue: number;
  activeClients: number;
  recentActivity: ActivityItem[];
}

export interface ActivityItem {
  id: string;
  type: ActivityType;
  description: string;
  timestamp: string;
  userId: string;
  userName: string;
}

export type ActivityType = 'estimate_created' | 'estimate_approved' | 'client_added' | 'template_used';

// Theme Types
export interface ThemeState {
  isDark: boolean;
  colors: ThemeColors;
}

export interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  success: string;
  warning: string;
  error: string;
}

// Navigation Types
export type RootStackParamList = {
  Welcome: undefined;
  Login: undefined;
  Register: undefined;
  Main: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Clients: undefined;
  Estimates: undefined;
  Templates: undefined;
  Profile: undefined;
};

export type EstimateStackParamList = {
  EstimateList: undefined;
  EstimateBuilder: { templateId?: string };
  EstimateDetail: { estimateId: string };
};

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  role: UserRole;
}

export interface ClientForm {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  company?: string;
  address: Address;
}

export interface EstimateForm {
  clientId: string;
  title: string;
  description: string;
  validUntil: string;
  items: EstimateItem[];
}
