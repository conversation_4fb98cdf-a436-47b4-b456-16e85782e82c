import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ThemeState, ThemeColors } from '../types';
import { lightTheme, darkTheme } from '../constants/Colors';

interface ThemeContextType {
  theme: ThemeState;
  toggleTheme: () => void;
  setTheme: (isDark: boolean) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setThemeState] = useState<ThemeState>({
    isDark: false,
    colors: lightTheme,
  });

  useEffect(() => {
    loadTheme();
  }, []);

  const loadTheme = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem('theme');
      if (savedTheme !== null) {
        const isDark = JSON.parse(savedTheme);
        setThemeState({
          isDark,
          colors: isDark ? darkTheme : lightTheme,
        });
      }
    } catch (error) {
      console.error('Error loading theme:', error);
    }
  };

  const saveTheme = async (isDark: boolean) => {
    try {
      await AsyncStorage.setItem('theme', JSON.stringify(isDark));
    } catch (error) {
      console.error('Error saving theme:', error);
    }
  };

  const toggleTheme = () => {
    const newIsDark = !theme.isDark;
    setThemeState({
      isDark: newIsDark,
      colors: newIsDark ? darkTheme : lightTheme,
    });
    saveTheme(newIsDark);
  };

  const setTheme = (isDark: boolean) => {
    setThemeState({
      isDark,
      colors: isDark ? darkTheme : lightTheme,
    });
    saveTheme(isDark);
  };

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
